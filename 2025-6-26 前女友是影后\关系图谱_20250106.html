<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前女友是影后 - 角色关系图谱</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .graph-container {
            width: 100%;
            height: 600px;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            margin-bottom: 30px;
            background: #f8f9fa;
        }
        .legend {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-bottom: 20px;
            padding: 20px;
            background: #ecf0f1;
            border-radius: 10px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px;
            padding: 8px 15px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid #333;
        }
        .info-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #e74c3c;
        }
        .info-card h3 {
            color: #e74c3c;
            margin-top: 0;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        .relationship-strength {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .strength-bar {
            width: 100px;
            height: 10px;
            background: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
        }
        .strength-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c);
            transition: width 0.3s ease;
        }
        .node-tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 前女友是影后 - 角色关系图谱</h1>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #e74c3c;"></div>
                <span>主角 - 程海</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #9b59b6;"></div>
                <span>女主 - 金慧</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #3498db;"></div>
                <span>男配 - 苏洲燃</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #95a5a6;"></div>
                <span>次要角色</span>
            </div>
        </div>
        
        <div class="graph-container" id="relationship-graph"></div>
        
        <div class="info-panel">
            <div class="info-card">
                <h3>💕 核心情感关系</h3>
                <div class="relationship-strength">
                    <span>程海 → 金慧</span>
                    <div class="strength-bar"><div class="strength-fill" style="width: 95%;"></div></div>
                    <span>95%</span>
                </div>
                <div class="relationship-strength">
                    <span>金慧 → 程海</span>
                    <div class="strength-bar"><div class="strength-fill" style="width: 100%;"></div></div>
                    <span>100%</span>
                </div>
                <div class="relationship-strength">
                    <span>苏洲燃 → 金慧</span>
                    <div class="strength-bar"><div class="strength-fill" style="width: 30%;"></div></div>
                    <span>30%</span>
                </div>
                <div class="relationship-strength">
                    <span>金慧 → 苏洲燃</span>
                    <div class="strength-bar"><div class="strength-fill" style="width: 25%;"></div></div>
                    <span>25%</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>⚡ 权力关系变化</h3>
                <p><strong>高中时期：</strong></p>
                <p>金慧(隐藏星二代) > 苏洲燃(富二代) > 程海(穷学生)</p>
                <p><strong>重逢时期：</strong></p>
                <p>金慧(影后) > 苏洲燃(富二代) > 程海(助理导演)</p>
                <p><strong>结局时期：</strong></p>
                <p>金慧(掌权影后) ≈ 程海(平等伴侣) > 苏洲燃(合作伙伴)</p>
            </div>
            
            <div class="info-card">
                <h3>🎭 角色成长轨迹</h3>
                <p><strong>程海：</strong> 自卑内向 → 逃避现实 → 勇敢面对 → 获得真爱</p>
                <p><strong>金慧：</strong> 叛逆少女 → 痛苦思念 → 主动追求 → 圆满幸福</p>
                <p><strong>苏洲燃：</strong> 自大占有 → 受到冲击 → 自我反思 → 学会放手</p>
            </div>
            
            <div class="info-card">
                <h3>🔗 关键关系节点</h3>
                <p><strong>初遇：</strong> 雨中撑伞，奠定守护主题</p>
                <p><strong>分离：</strong> 误解与欺骗，七年痛苦</p>
                <p><strong>重逢：</strong> 身份反转，角色互换</p>
                <p><strong>真相：</strong> 误会解除，真爱回归</p>
                <p><strong>结局：</strong> 梧桐树下，完美收官</p>
            </div>
        </div>
    </div>

    <script>
        // 创建关系图谱
        const width = document.getElementById('relationship-graph').clientWidth;
        const height = 600;
        
        const svg = d3.select('#relationship-graph')
            .append('svg')
            .attr('width', width)
            .attr('height', height);
            
        // 定义节点数据
        const nodes = [
            {id: '程海', group: 1, size: 25, color: '#e74c3c', description: '男主角，25岁，助理导演→副导演，从自卑到自信的成长'},
            {id: '金慧', group: 2, size: 25, color: '#9b59b6', description: '女主角，25岁，影后，从叛逆少女到成熟女性'},
            {id: '苏洲燃', group: 3, size: 20, color: '#3498db', description: '男配角，富二代，从占有到放手的觉醒'},
            {id: '程海姨妈', group: 4, size: 15, color: '#95a5a6', description: '抚养者，真相揭露者'},
            {id: '金慧父母', group: 4, size: 15, color: '#95a5a6', description: '阻碍者，权威象征'},
            {id: '经纪人', group: 4, size: 12, color: '#95a5a6', description: '信息传递者，忠诚朋友'},
            {id: '剧组同事', group: 4, size: 10, color: '#95a5a6', description: '氛围营造者，外界视角'}
        ];
        
        // 定义连接数据
        const links = [
            {source: '程海', target: '金慧', strength: 95, type: '真爱', color: '#e74c3c'},
            {source: '金慧', target: '程海', strength: 100, type: '执着爱恋', color: '#9b59b6'},
            {source: '苏洲燃', target: '金慧', strength: 30, type: '兄妹情谊', color: '#3498db'},
            {source: '金慧', target: '苏洲燃', strength: 25, type: '感恩但拒绝', color: '#95a5a6'},
            {source: '程海', target: '程海姨妈', strength: 60, type: '抚养关系', color: '#95a5a6'},
            {source: '金慧', target: '金慧父母', strength: 40, type: '叛逆关系', color: '#95a5a6'},
            {source: '金慧', target: '经纪人', strength: 70, type: '朋友关系', color: '#95a5a6'},
            {source: '程海', target: '剧组同事', strength: 50, type: '工作关系', color: '#95a5a6'}
        ];
        
        // 创建力导向图
        const simulation = d3.forceSimulation(nodes)
            .force('link', d3.forceLink(links).id(d => d.id).distance(100))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2));
            
        // 创建连接线
        const link = svg.append('g')
            .selectAll('line')
            .data(links)
            .enter().append('line')
            .attr('stroke', d => d.color)
            .attr('stroke-width', d => d.strength / 10)
            .attr('stroke-opacity', 0.6);
            
        // 创建节点
        const node = svg.append('g')
            .selectAll('circle')
            .data(nodes)
            .enter().append('circle')
            .attr('r', d => d.size)
            .attr('fill', d => d.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 3)
            .call(d3.drag()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended));
                
        // 添加节点标签
        const label = svg.append('g')
            .selectAll('text')
            .data(nodes)
            .enter().append('text')
            .text(d => d.id)
            .attr('font-size', 14)
            .attr('font-weight', 'bold')
            .attr('text-anchor', 'middle')
            .attr('dy', 5)
            .attr('fill', '#2c3e50');
            
        // 创建工具提示
        const tooltip = d3.select('body').append('div')
            .attr('class', 'node-tooltip')
            .style('opacity', 0);
            
        // 添加鼠标事件
        node.on('mouseover', function(event, d) {
            tooltip.transition().duration(200).style('opacity', .9);
            tooltip.html(d.description)
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 28) + 'px');
        })
        .on('mouseout', function(d) {
            tooltip.transition().duration(500).style('opacity', 0);
        });
        
        // 更新位置
        simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);
                
            node
                .attr('cx', d => d.x)
                .attr('cy', d => d.y);
                
            label
                .attr('x', d => d.x)
                .attr('y', d => d.y);
        });
        
        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
    </script>
</body>
</html>