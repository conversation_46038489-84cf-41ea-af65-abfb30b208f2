<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《舔狗不装了》关系图谱 V20250709</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        .network-graph {
            position: relative;
            height: 600px;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }
        .character {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
        .character:hover {
            transform: scale(1.1);
            z-index: 10;
        }
        .jianghe {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            top: 250px;
            left: 500px;
        }
        .jiangyan {
            background: linear-gradient(45deg, #A8E6CF, #FFD93D);
            top: 100px;
            left: 200px;
        }
        .guqianqian {
            background: linear-gradient(45deg, #FF8A80, #FF80AB);
            top: 400px;
            left: 800px;
        }
        .relationship-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform-origin: left center;
            z-index: 1;
        }
        .line1 { /* 江鹤 → 姜妍 */
            top: 310px;
            left: 500px;
            width: 280px;
            transform: rotate(-25deg);
            opacity: 0.6;
        }
        .line2 { /* 江鹤 → 顾倩倩 */
            top: 310px;
            left: 620px;
            width: 250px;
            transform: rotate(25deg);
            opacity: 0.9;
        }
        .relationship-label {
            position: absolute;
            background: rgba(255,255,255,0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .label1 {
            top: 200px;
            left: 350px;
            color: #e74c3c;
        }
        .label2 {
            top: 420px;
            left: 650px;
            color: #27ae60;
        }
        .stats-panel {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .character-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .progress-bar {
            width: 100px;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
        .timeline {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>《舔狗不装了》动态关系网络图</h1>
            <p>版本：V20250709 | 分析深度：标准级 | 情感强度实时监测</p>
        </div>

        <div class="network-graph">
            <!-- 角色节点 -->
            <div class="character jianghe" onclick="showCharacterInfo('江鹤')">
                江鹤<br><small>核心主角</small>
            </div>
            <div class="character jiangyan" onclick="showCharacterInfo('姜妍')">
                姜妍<br><small>前女神</small>
            </div>
            <div class="character guqianqian" onclick="showCharacterInfo('顾倩倩')">
                顾倩倩<br><small>真爱对象</small>
            </div>

            <!-- 关系连线 -->
            <div class="relationship-line line1"></div>
            <div class="relationship-line line2"></div>

            <!-- 关系标签 -->
            <div class="relationship-label label1">
                决裂关系<br>信任值：0/100<br>敌意值：85/100
            </div>
            <div class="relationship-label label2">
                恋爱关系<br>信任值：95/100<br>亲密值：90/100
            </div>
        </div>

        <div class="stats-panel">
            <div class="character-stats">
                <h3>🎭 江鹤 - 情感数据</h3>
                <div class="stat-item">
                    <span>对姜妍情感值</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 5%"></div></div>
                </div>
                <div class="stat-item">
                    <span>对顾倩倩情感值</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 90%"></div></div>
                </div>
                <div class="stat-item">
                    <span>理性控制力</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 85%"></div></div>
                </div>
                <div class="stat-item">
                    <span>道德底线</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 70%"></div></div>
                </div>
            </div>

            <div class="character-stats">
                <h3>👑 姜妍 - 情感数据</h3>
                <div class="stat-item">
                    <span>对江鹤执着度</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 95%"></div></div>
                </div>
                <div class="stat-item">
                    <span>自尊受损度</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 88%"></div></div>
                </div>
                <div class="stat-item">
                    <span>挽回成功率</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 2%"></div></div>
                </div>
                <div class="stat-item">
                    <span>深情真实度</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 75%"></div></div>
                </div>
            </div>

            <div class="character-stats">
                <h3>🌸 顾倩倩 - 情感数据</h3>
                <div class="stat-item">
                    <span>对江鹤依恋度</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 98%"></div></div>
                </div>
                <div class="stat-item">
                    <span>成长进步值</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 85%"></div></div>
                </div>
                <div class="stat-item">
                    <span>叛逆程度</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 25%"></div></div>
                </div>
                <div class="stat-item">
                    <span>真心纯度</span>
                    <div class="progress-bar"><div class="progress-fill" style="width: 95%"></div></div>
                </div>
            </div>
        </div>

        <div class="timeline">
            <h3>📊 关系演变时间轴</h3>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div>
                    <strong>第1-3章</strong>：江鹤→姜妍（伪装舔狗关系，信任值60，真实度0）
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div>
                    <strong>第4章</strong>：关系破裂，真相暴露（信任值降至10，敌意值升至70）
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div>
                    <strong>第6-10章</strong>：江鹤↔顾倩倩（师生关系转恋爱，亲密值逐步上升至80）
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div>
                    <strong>第11-18章</strong>：姜妍→江鹤（单方面追求，执着度95，成功率5）
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div>
                    <strong>第19-21章</strong>：最终选择确定（江鹤↔顾倩倩稳定关系，信任值95）
                </div>
            </div>
        </div>
    </div>

    <script>
        function showCharacterInfo(name) {
            const info = {
                '江鹤': '核心主角，经历从虚假到真实的情感转变，最终选择真爱。',
                '姜妍': '前女神，迟来的深情未能挽回失去的爱情，独自承受失败。',
                '顾倩倩': '真爱对象，从叛逆少女成长为成熟女性，获得真正的爱情。'
            };
            alert(`${name}：${info[name]}`);
        }

        // 动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const lines = document.querySelectorAll('.relationship-line');
            lines.forEach(line => {
                line.style.animation = 'pulse 2s infinite';
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { opacity: 0.6; }
                50% { opacity: 1; }
                100% { opacity: 0.6; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
