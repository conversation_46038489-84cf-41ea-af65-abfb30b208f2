<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《甩了我后她后悔了》关系图谱</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .relationship-map {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 500px;
            position: relative;
            margin: 40px 0;
        }
        .character {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            position: absolute;
            text-align: center;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        .character:hover {
            transform: scale(1.1);
        }
        .luoning {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }
        .shenlin {
            background: linear-gradient(45deg, #e91e63, #c2185b);
            top: 20%;
            left: 20%;
            transform: translate(-50%, -50%);
        }
        .qixue {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            top: 20%;
            right: 20%;
            transform: translate(50%, -50%);
        }
        .liansheng {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            bottom: 20%;
            left: 20%;
            transform: translate(-50%, 50%);
        }
        .relationship-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            transform-origin: left center;
            z-index: 1;
        }
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        .stat-card h3 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .relationship-detail {
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid #667eea;
        }
        .emotion-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        .emotion-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        .love { background: linear-gradient(90deg, #ff6b6b, #ee5a52); }
        .obsession { background: linear-gradient(90deg, #a8e6cf, #7fcdcd); }
        .despair { background: linear-gradient(90deg, #ffd93d, #ff6b6b); }
        .happiness { background: linear-gradient(90deg, #4ecdc4, #44a08d); }
        
        .timeline {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .timeline h3 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }
        .timeline-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .timeline-time {
            font-weight: bold;
            color: #667eea;
            min-width: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>《甩了我后她后悔了》关系图谱</h1>
        
        <div class="relationship-map">
            <div class="character luoning">洛宁<br><small>主角</small></div>
            <div class="character shenlin">沈琳<br><small>前女友</small></div>
            <div class="character qixue">齐雪<br><small>真爱</small></div>
            <div class="character liansheng">连笙<br><small>白月光</small></div>
        </div>

        <div class="stats-panel">
            <div class="stat-card">
                <h3>洛宁 → 沈琳</h3>
                <div class="relationship-detail">
                    <strong>初期关系：</strong>迷恋替身
                    <div class="emotion-bar">
                        <div class="emotion-fill love" style="width: 90%"></div>
                    </div>
                    迷恋值：90/100
                </div>
                <div class="relationship-detail">
                    <strong>中期关系：</strong>习惯依赖
                    <div class="emotion-bar">
                        <div class="emotion-fill obsession" style="width: 70%"></div>
                    </div>
                    习惯值：70/100
                </div>
                <div class="relationship-detail">
                    <strong>后期关系：</strong>感激决绝
                    <div class="emotion-bar">
                        <div class="emotion-fill despair" style="width: 40%"></div>
                    </div>
                    感激值：40/100
                </div>
            </div>

            <div class="stat-card">
                <h3>沈琳 → 洛宁</h3>
                <div class="relationship-detail">
                    <strong>初期关系：</strong>利用忽视
                    <div class="emotion-bar">
                        <div class="emotion-fill despair" style="width: 60%"></div>
                    </div>
                    利用值：60/100
                </div>
                <div class="relationship-detail">
                    <strong>中期关系：</strong>依赖心虚
                    <div class="emotion-bar">
                        <div class="emotion-fill obsession" style="width: 50%"></div>
                    </div>
                    依赖值：50/100
                </div>
                <div class="relationship-detail">
                    <strong>后期关系：</strong>爱而不得
                    <div class="emotion-bar">
                        <div class="emotion-fill love" style="width: 85%"></div>
                    </div>
                    爱意值：85/100
                </div>
            </div>

            <div class="stat-card">
                <h3>洛宁 → 齐雪</h3>
                <div class="relationship-detail">
                    <strong>全程关系：</strong>真爱至上
                    <div class="emotion-bar">
                        <div class="emotion-fill happiness" style="width: 100%"></div>
                    </div>
                    真爱值：100/100
                </div>
                <div class="relationship-detail">
                    <strong>依赖程度：</strong>相互依赖
                    <div class="emotion-bar">
                        <div class="emotion-fill happiness" style="width: 90%"></div>
                    </div>
                    依赖值：90/100
                </div>
                <div class="relationship-detail">
                    <strong>幸福指数：</strong>圆满结局
                    <div class="emotion-bar">
                        <div class="emotion-fill happiness" style="width: 95%"></div>
                    </div>
                    幸福值：95/100
                </div>
            </div>

            <div class="stat-card">
                <h3>沈琳 → 连笙</h3>
                <div class="relationship-detail">
                    <strong>全程关系：</strong>执念痴迷
                    <div class="emotion-bar">
                        <div class="emotion-fill obsession" style="width: 95%"></div>
                    </div>
                    执念值：95/100
                </div>
                <div class="relationship-detail">
                    <strong>最终结果：</strong>彻底放弃
                    <div class="emotion-bar">
                        <div class="emotion-fill despair" style="width: 100%"></div>
                    </div>
                    放弃值：100/100
                </div>
            </div>
        </div>

        <div class="timeline">
            <h3>关键转折点时间轴</h3>
            <div class="timeline-item">
                <div class="timeline-time">第1章</div>
                <div>沈琳追白月光出国，洛宁求而不得</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第3章</div>
                <div>沈琳回国视频通话，向连笙介绍洛宁</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第4章</div>
                <div>齐雪回归，洛宁找到真爱</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第5章</div>
                <div>沈琳深夜电话质问，称洛宁为骗子</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第8章</div>
                <div>洛宁齐雪结婚，沈琳现身婚礼</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第10章</div>
                <div>新婚夜后沈琳守候一夜，彻底崩溃</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第11章</div>
                <div>海岛蜜月，验孕棒照片终结一切</div>
            </div>
            <div class="timeline-item">
                <div class="timeline-time">第12章</div>
                <div>沈琳送金锁和戒指，彻底告别</div>
            </div>
        </div>
    </div>
</body>
</html>
