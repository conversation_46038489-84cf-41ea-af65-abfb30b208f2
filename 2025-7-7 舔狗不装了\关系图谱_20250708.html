<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《舔狗不装了》关系图谱 - V20250708</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        .relationship-map {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }
        .character {
            background: white;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            position: relative;
            z-index: 10;
        }
        .character.protagonist { background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: white; }
        .character.past { background: linear-gradient(45deg, #a55eea, #8e44ad); color: white; }
        .character.future { background: linear-gradient(45deg, #26de81, #20bf6b); color: white; }
        .character.catalyst { background: linear-gradient(45deg, #feca57, #ff9ff3); color: white; }
        
        .connection {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #a55eea);
            z-index: 1;
        }
        .connection.strong { height: 5px; }
        .connection.weak { height: 2px; opacity: 0.6; }
        
        .emotion-matrix {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 40px 0;
        }
        .emotion-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .emotion-bar {
            background: #f1f2f6;
            height: 20px;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        .emotion-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .love { background: linear-gradient(90deg, #ff6b6b, #ee5a24); }
        .trust { background: linear-gradient(90deg, #26de81, #20bf6b); }
        .obsession { background: linear-gradient(90deg, #a55eea, #8e44ad); }
        .rejection { background: linear-gradient(90deg, #ff3838, #c44569); }
        
        .timeline {
            margin: 40px 0;
        }
        .timeline-item {
            display: flex;
            margin: 20px 0;
            align-items: center;
        }
        .timeline-dot {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #667eea;
            margin-right: 20px;
        }
        .timeline-content {
            flex: 1;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .legend {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 10px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>《舔狗不装了》动态关系图谱</h1>
            <p>版本：V20250708 | 分析时间：2025-07-08 | 角色数：4 | 关系线：6</p>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color protagonist"></div>
                <span>主角 (江鹤)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color past"></div>
                <span>过去 (姜妍)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color future"></div>
                <span>未来 (顾倩倩)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color catalyst"></div>
                <span>催化剂 (顾凡)</span>
            </div>
        </div>
        
        <div class="relationship-map">
            <div class="character past">姜妍<br><small>情场老手</small></div>
            <div class="character protagonist">江鹤<br><small>理性选择者</small></div>
            <div class="character future">顾倩倩<br><small>成长少女</small></div>
            <div class="character catalyst" style="position: absolute; top: -60px; left: 50%; transform: translateX(-50%);">顾凡<br><small>金钱推手</small></div>
        </div>
        
        <div class="emotion-matrix">
            <div class="emotion-card">
                <h3>江鹤 → 姜妍</h3>
                <div>初期伪装爱意 (85%)</div>
                <div class="emotion-bar"><div class="emotion-fill love" style="width: 85%"></div></div>
                <div>真实厌恶程度 (20%)</div>
                <div class="emotion-bar"><div class="emotion-fill rejection" style="width: 20%"></div></div>
                <div>最终决绝程度 (100%)</div>
                <div class="emotion-bar"><div class="emotion-fill rejection" style="width: 100%"></div></div>
            </div>
            
            <div class="emotion-card">
                <h3>姜妍 → 江鹤</h3>
                <div>初期利用程度 (70%)</div>
                <div class="emotion-bar"><div class="emotion-fill obsession" style="width: 70%"></div></div>
                <div>觉醒后真心 (95%)</div>
                <div class="emotion-bar"><div class="emotion-fill love" style="width: 95%"></div></div>
                <div>最终绝望程度 (100%)</div>
                <div class="emotion-bar"><div class="emotion-fill rejection" style="width: 100%"></div></div>
            </div>
            
            <div class="emotion-card">
                <h3>江鹤 → 顾倩倩</h3>
                <div>初期职责感 (60%)</div>
                <div class="emotion-bar"><div class="emotion-fill trust" style="width: 60%"></div></div>
                <div>发展期真心 (60%)</div>
                <div class="emotion-bar"><div class="emotion-fill love" style="width: 60%"></div></div>
                <div>最终真爱程度 (90%)</div>
                <div class="emotion-bar"><div class="emotion-fill love" style="width: 90%"></div></div>
            </div>
            
            <div class="emotion-card">
                <h3>顾倩倩 → 江鹤</h3>
                <div>始终真心程度 (100%)</div>
                <div class="emotion-bar"><div class="emotion-fill love" style="width: 100%"></div></div>
                <div>占有欲强度 (90%)</div>
                <div class="emotion-bar"><div class="emotion-fill obsession" style="width: 90%"></div></div>
                <div>成长动力 (100%)</div>
                <div class="emotion-bar"><div class="emotion-fill trust" style="width: 100%"></div></div>
            </div>
        </div>
        
        <div class="timeline">
            <h3>关系发展时间轴</h3>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <strong>第01章：</strong>江鹤撕下舔狗面具，开始真实自我展现
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <strong>第04章：</strong>姜妍发现五千万真相，双方关系彻底暴露
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <strong>第07章：</strong>顾倩倩登场，新的情感线开启
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <strong>第12章：</strong>姜妍医院真心告白，江鹤首次动摇
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <strong>第17章：</strong>姜妍车祸救人，情感天平倾斜
                </div>
            </div>
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <strong>第20章：</strong>江鹤最终选择顾倩倩，承诺陪伴成长
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px; color: #666;">
            <p>数据来源：《舔狗不装了》全文分析 | 生成时间：2025-07-08</p>
            <p>关联文件：舔狗不装了_剧情点拆解_20250708.md | 角色宝典_20250708.json</p>
        </div>
    </div>
</body>
</html>
