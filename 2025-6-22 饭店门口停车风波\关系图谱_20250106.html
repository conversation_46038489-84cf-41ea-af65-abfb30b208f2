<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>饭店门口停车风波 - 关系图谱</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        #graph {
            width: 100%;
            height: 600px;
            border: 2px solid #34495e;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            stroke-width: 3px;
            filter: brightness(1.2);
        }
        
        .link {
            stroke-opacity: 0.8;
            transition: all 0.3s ease;
        }
        
        .link:hover {
            stroke-width: 4px;
            stroke-opacity: 1;
        }
        
        .node-label {
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        
        .legend {
            margin-top: 20px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 10px;
            padding: 10px;
            background: rgba(52, 73, 94, 0.1);
            border-radius: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid #2c3e50;
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 20px;
            background: rgba(52, 73, 94, 0.05);
            border-radius: 10px;
            border-left: 5px solid #3498db;
        }
        
        .relationship-strength {
            margin-top: 15px;
        }
        
        .strength-bar {
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .strength-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 饭店门口停车风波 - 角色关系图谱</h1>
        
        <div id="graph"></div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #e74c3c;"></div>
                <span>主角（父亲）</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #f39c12;"></div>
                <span>女儿诺诺</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #2ecc71;"></div>
                <span>战友团体</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #8e44ad;"></div>
                <span>反派角色</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #95a5a6;"></div>
                <span>围观群众</span>
            </div>
        </div>
        
        <div class="info-panel">
            <h3>🔍 关系强度分析</h3>
            <div class="relationship-strength">
                <div><strong>父女情深：</strong></div>
                <div class="strength-bar">
                    <div class="strength-fill" style="width: 100%; background: #e74c3c;"></div>
                </div>
                <span>100/100 - 无条件的爱与保护</span>
            </div>
            
            <div class="relationship-strength">
                <div><strong>战友情义：</strong></div>
                <div class="strength-bar">
                    <div class="strength-fill" style="width: 95%; background: #2ecc71;"></div>
                </div>
                <span>95/100 - 生死与共的兄弟情</span>
            </div>
            
            <div class="relationship-strength">
                <div><strong>敌对仇恨：</strong></div>
                <div class="strength-bar">
                    <div class="strength-fill" style="width: 100%; background: #8e44ad;"></div>
                </div>
                <span>100/100 - 不共戴天的仇恨</span>
            </div>
            
            <div class="relationship-strength">
                <div><strong>社会冷漠：</strong></div>
                <div class="strength-bar">
                    <div class="strength-fill" style="width: 80%; background: #95a5a6;"></div>
                </div>
                <span>80/100 - 事不关己高高挂起</span>
            </div>
        </div>
    </div>

    <script>
        // 图谱数据
        const nodes = [
            { id: "主角", group: 1, size: 25, color: "#e74c3c", description: "退伍军人父亲" },
            { id: "诺诺", group: 2, size: 20, color: "#f39c12", description: "4岁女儿" },
            { id: "摩托车手", group: 3, size: 18, color: "#2ecc71", description: "杜卡迪骑手" },
            { id: "路虎司机", group: 3, size: 16, color: "#2ecc71", description: "冲锋队长" },
            { id: "其他战友", group: 3, size: 15, color: "#2ecc71", description: "兄弟团体" },
            { id: "饭店老板", group: 4, size: 22, color: "#8e44ad", description: "暴躁老板" },
            { id: "老板娘", group: 4, size: 18, color: "#8e44ad", description: "恶毒老板娘" },
            { id: "饭店员工", group: 4, size: 12, color: "#8e44ad", description: "帮凶群体" },
            { id: "围观群众", group: 5, size: 15, color: "#95a5a6", description: "冷漠路人" }
        ];

        const links = [
            // 父女关系
            { source: "主角", target: "诺诺", value: 100, type: "父女情", color: "#e74c3c", width: 5 },
            
            // 战友关系
            { source: "主角", target: "摩托车手", value: 95, type: "战友情", color: "#2ecc71", width: 4 },
            { source: "主角", target: "路虎司机", value: 95, type: "战友情", color: "#2ecc71", width: 4 },
            { source: "主角", target: "其他战友", value: 90, type: "战友情", color: "#2ecc71", width: 3 },
            { source: "摩托车手", target: "路虎司机", value: 85, type: "战友情", color: "#2ecc71", width: 2 },
            { source: "摩托车手", target: "其他战友", value: 85, type: "战友情", color: "#2ecc71", width: 2 },
            
            // 敌对关系
            { source: "主角", target: "饭店老板", value: 100, type: "仇恨", color: "#c0392b", width: 5 },
            { source: "主角", target: "老板娘", value: 95, type: "仇恨", color: "#c0392b", width: 4 },
            { source: "诺诺", target: "饭店老板", value: 90, type: "受害", color: "#e67e22", width: 3 },
            { source: "诺诺", target: "老板娘", value: 95, type: "受害", color: "#e67e22", width: 4 },
            
            // 夫妻关系（破裂）
            { source: "饭店老板", target: "老板娘", value: 60, type: "夫妻（破裂）", color: "#8e44ad", width: 2 },
            
            // 雇佣关系
            { source: "饭店老板", target: "饭店员工", value: 70, type: "雇佣", color: "#9b59b6", width: 2 },
            { source: "老板娘", target: "饭店员工", value: 65, type: "雇佣", color: "#9b59b6", width: 2 },
            
            // 冷漠关系
            { source: "围观群众", target: "主角", value: 30, type: "冷漠", color: "#95a5a6", width: 1 },
            { source: "围观群众", target: "诺诺", value: 20, type: "冷漠", color: "#95a5a6", width: 1 }
        ];

        // 创建SVG
        const width = document.getElementById('graph').clientWidth;
        const height = 600;

        const svg = d3.select("#graph")
            .append("svg")
            .attr("width", width)
            .attr("height", height);

        // 创建力导向图
        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(d => 150 - d.value))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(d => d.size + 5));

        // 添加连线
        const link = svg.append("g")
            .selectAll("line")
            .data(links)
            .enter().append("line")
            .attr("class", "link")
            .attr("stroke", d => d.color)
            .attr("stroke-width", d => d.width)
            .attr("stroke-dasharray", d => d.type === "冷漠" ? "5,5" : "none");

        // 添加节点
        const node = svg.append("g")
            .selectAll("circle")
            .data(nodes)
            .enter().append("circle")
            .attr("class", "node")
            .attr("r", d => d.size)
            .attr("fill", d => d.color)
            .attr("stroke", "#2c3e50")
            .attr("stroke-width", 2)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        // 添加标签
        const label = svg.append("g")
            .selectAll("text")
            .data(nodes)
            .enter().append("text")
            .attr("class", "node-label")
            .text(d => d.id)
            .attr("font-size", d => Math.max(10, d.size / 2))
            .attr("fill", "#2c3e50");

        // 添加工具提示
        node.append("title")
            .text(d => `${d.id}\n${d.description}`);

        link.append("title")
            .text(d => `${d.source.id} → ${d.target.id}\n关系: ${d.type}\n强度: ${d.value}/100`);

        // 更新位置
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("cx", d => Math.max(d.size, Math.min(width - d.size, d.x)))
                .attr("cy", d => Math.max(d.size, Math.min(height - d.size, d.y)));

            label
                .attr("x", d => Math.max(d.size, Math.min(width - d.size, d.x)))
                .attr("y", d => Math.max(d.size, Math.min(height - d.size, d.y)));
        });

        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // 响应式调整
        window.addEventListener('resize', () => {
            const newWidth = document.getElementById('graph').clientWidth;
            svg.attr('width', newWidth);
            simulation.force('center', d3.forceCenter(newWidth / 2, height / 2));
            simulation.alpha(0.3).restart();
        });
    </script>
</body>
</html>