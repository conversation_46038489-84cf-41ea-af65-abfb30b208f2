<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《山上的怪物》角色关系图谱</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 30px;
        }
        .graph-container {
            position: relative;
            width: 100%;
            height: 600px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }
        .node {
            position: absolute;
            padding: 10px 15px;
            border-radius: 25px;
            text-align: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        .node:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0,0,0,0.5);
        }
        .protagonist {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            top: 250px;
            left: 500px;
            font-size: 16px;
        }
        .parents {
            background: linear-gradient(45deg, #4834d4, #686de0);
            font-size: 14px;
        }
        .father {
            top: 100px;
            left: 350px;
        }
        .mother {
            top: 100px;
            left: 650px;
        }
        .caregiver {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            top: 400px;
            left: 500px;
            font-size: 14px;
        }
        .helper {
            background: linear-gradient(45deg, #5f27cd, #a55eea);
            top: 250px;
            left: 200px;
            font-size: 14px;
        }
        .monster {
            background: linear-gradient(45deg, #2d3436, #636e72);
            top: 450px;
            left: 800px;
            font-size: 14px;
            color: #ff7675;
        }
        .connection {
            position: absolute;
            height: 2px;
            background: #fff;
            transform-origin: left center;
            opacity: 0.7;
        }
        .connection.blood {
            background: #ff6b6b;
            height: 3px;
        }
        .connection.care {
            background: #00d2d3;
            height: 3px;
        }
        .connection.enemy {
            background: #2d3436;
            height: 4px;
        }
        .connection.fake {
            background: #a55eea;
            height: 2px;
            opacity: 0.5;
            border-style: dashed;
        }
        .legend {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px;
        }
        .legend-line {
            width: 30px;
            height: 3px;
            margin-right: 10px;
        }
        .info-panel {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .relationship-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #ff6b6b;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #ff6b6b;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>《山上的怪物》角色关系图谱</h1>
        
        <div class="graph-container">
            <!-- 角色节点 -->
            <div class="node protagonist" data-info="主角：赵春生/春妮<br>真实身份：连体双胞胎幸存者<br>状态：被怪物寄生控制">
                春生/春妮<br><small>主角</small>
            </div>
            
            <div class="node parents father" data-info="父亲：赵民强<br>职业：猎人<br>状态：十年前已死（幻象出现）">
                赵民强<br><small>父亲</small>
            </div>
            
            <div class="node parents mother" data-info="母亲：吴翠兰<br>状态：十年前已死（幻象出现）<br>经历：怀孕时被怪物袭击">
                吴翠兰<br><small>母亲</small>
            </div>
            
            <div class="node caregiver" data-info="马婶<br>关系：养母<br>状态：被怪物杀死<br>特征：两次丧夫，抚养春生十年">
                马婶<br><small>养母</small>
            </div>
            
            <div class="node helper" data-info="六子叔：赵六<br>状态：十年前已死<br>出现形式：怪物制造的幻象<br>作用：引导春生，揭示真相">
                六子叔<br><small>引导者</small>
            </div>
            
            <div class="node monster" data-info="怪物母体+幼体<br>复仇动机：幼崽被误杀<br>能力：寄生、精神控制、幻象制造<br>目标：跨代复仇">
                怪物<br><small>终极敌人</small>
            </div>
            
            <!-- 连接线 -->
            <div class="connection blood" style="top: 150px; left: 420px; width: 180px; transform: rotate(0deg);"></div>
            <div class="connection blood" style="top: 200px; left: 420px; width: 120px; transform: rotate(45deg);"></div>
            <div class="connection blood" style="top: 200px; left: 580px; width: 120px; transform: rotate(-45deg);"></div>
            
            <div class="connection care" style="top: 350px; left: 500px; width: 80px; transform: rotate(90deg);"></div>
            
            <div class="connection fake" style="top: 250px; left: 270px; width: 180px; transform: rotate(0deg);"></div>
            
            <div class="connection enemy" style="top: 350px; left: 650px; width: 200px; transform: rotate(45deg);"></div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-line" style="background: #ff6b6b;"></div>
                <span>血缘关系</span>
            </div>
            <div class="legend-item">
                <div class="legend-line" style="background: #00d2d3;"></div>
                <span>养育关系</span>
            </div>
            <div class="legend-item">
                <div class="legend-line" style="background: #2d3436;"></div>
                <span>敌对关系</span>
            </div>
            <div class="legend-item">
                <div class="legend-line" style="background: #a55eea; opacity: 0.5;"></div>
                <span>虚假关系</span>
            </div>
        </div>
        
        <div class="info-panel">
            <h2>关系强度分析</h2>
            <div class="relationship-stats">
                <div class="stat-card">
                    <h3>马婶 → 春生</h3>
                    <p>养育关系 | 情感强度</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <small>95/100 - 十年养育，深厚母爱</small>
                </div>
                
                <div class="stat-card">
                    <h3>怪物 ↔ 赵民强</h3>
                    <p>敌对关系 | 仇恨值</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%; background: #2d3436;"></div>
                    </div>
                    <small>100/100 - 杀子之仇，不共戴天</small>
                </div>
                
                <div class="stat-card">
                    <h3>怪物幼体 → 春生</h3>
                    <p>寄生关系 | 控制度</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%; background: #636e72;"></div>
                    </div>
                    <small>85/100 - 逐渐完全控制</small>
                </div>
                
                <div class="stat-card">
                    <h3>"六子叔" → 春生</h3>
                    <p>虚假关系 | 真实度</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%; background: #a55eea;"></div>
                    </div>
                    <small>0/100 - 完全虚假的幻象</small>
                </div>
            </div>
        </div>
        
        <div class="info-panel">
            <h2>核心冲突分析</h2>
            <h3>🔥 主要冲突线</h3>
            <p><strong>怪物复仇 vs 人类生存</strong></p>
            <ul>
                <li><strong>起因</strong>：十年前赵民强误杀怪物幼崽</li>
                <li><strong>手段</strong>：怪物通过寄生方式进行跨代复仇</li>
                <li><strong>过程</strong>：制造幻象，精神操控，逐步瓦解春生的现实认知</li>
                <li><strong>结果</strong>：春生意识被完全吞噬，怪物完成蜕变</li>
            </ul>
            
            <h3>🎭 真实与幻象</h3>
            <p><strong>现实层次的复杂交织</strong></p>
            <ul>
                <li><strong>真实存在</strong>：马婶（养母）、春生本体</li>
                <li><strong>死者幻象</strong>：父母、六子叔、村民</li>
                <li><strong>操控者</strong>：寄生在春生体内的怪物幼体</li>
                <li><strong>最终真相</strong>：所有温暖都是虚假，只有恐怖是真实</li>
            </ul>
            
            <h3>🔄 因果循环</h3>
            <p><strong>复仇的代际传承</strong></p>
            <ul>
                <li><strong>第一代</strong>：人类误杀怪物幼崽</li>
                <li><strong>第二代</strong>：怪物报复杀死人类成年人</li>
                <li><strong>第三代</strong>：怪物寄生人类幼儿，完成最终复仇</li>
                <li><strong>未来</strong>：新的怪物个体诞生，循环可能继续</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 添加交互效果
        document.querySelectorAll('.node').forEach(node => {
            node.addEventListener('mouseenter', function() {
                const info = this.getAttribute('data-info');
                if (info) {
                    // 可以在这里添加悬浮提示框
                    this.title = info.replace(/<br>/g, '\n');
                }
            });
        });
        
        // 动态效果
        window.addEventListener('load', function() {
            document.querySelectorAll('.progress-fill').forEach(fill => {
                const width = fill.style.width;
                fill.style.width = '0%';
                setTimeout(() => {
                    fill.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>