<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>艳鬼 - 角色关系图谱</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .legend {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            margin: 5px 15px;
            display: flex;
            align-items: center;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
        }
        #graph {
            width: 100%;
            height: 600px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            background: rgba(0,0,0,0.2);
        }
        .node {
            cursor: pointer;
            stroke: #fff;
            stroke-width: 2px;
        }
        .link {
            stroke-width: 2px;
            marker-end: url(#arrowhead);
        }
        .node-label {
            font-size: 12px;
            font-weight: bold;
            text-anchor: middle;
            fill: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        .link-label {
            font-size: 10px;
            fill: #ffeb3b;
            text-anchor: middle;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 200px;
        }
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌙 艳鬼 - 角色关系图谱 🌙</h1>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #ff6b6b;"></div>
                <span>主角</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #4ecdc4;"></div>
                <span>女主/反派</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #45b7d1;"></div>
                <span>重要配角</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #96ceb4;"></div>
                <span>次要角色</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #feca57;"></div>
                <span>反派角色</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #ff9ff3;"></div>
                <span>边缘角色</span>
            </div>
        </div>
        
        <svg id="graph"></svg>
        
        <div class="tooltip" id="tooltip"></div>
        
        <div class="info-panel">
            <h3>📊 关系网络统计</h3>
            <p><strong>角色总数：</strong>8个</p>
            <p><strong>关系连接：</strong>12条</p>
            <p><strong>核心节点：</strong>贵儿、小翠</p>
            <p><strong>关系类型：</strong>爱情、仇恨、保护、指导</p>
        </div>
    </div>

    <script>
        // 数据定义
        const nodes = [
            { id: "贵儿", group: "主角", color: "#ff6b6b", size: 25, description: "主角，属羊青年，被迫参与阴亲仪式" },
            { id: "小翠", group: "女主/反派", color: "#4ecdc4", size: 23, description: "村花，被害致死后化为艳鬼复仇" },
            { id: "三叔公", group: "重要配角", color: "#45b7d1", size: 20, description: "村中长老，丧娶管事，民间法师" },
            { id: "贵儿父亲", group: "次要角色", color: "#96ceb4", size: 15, description: "贵儿的父亲，急性子的普通村民" },
            { id: "虎子", group: "反派角色", color: "#feca57", size: 18, description: "村中混混，强奸犯之一，首个被报复" },
            { id: "二狗", group: "反派角色", color: "#feca57", size: 18, description: "村中混混，强奸犯之一，被戳瞎双眼" },
            { id: "阿彪", group: "反派角色", color: "#feca57", size: 18, description: "村中混混，强奸犯之一，木棍穿透而死" },
            { id: "连香", group: "边缘角色", color: "#ff9ff3", size: 12, description: "小翠的母亲，重男轻女，对女儿刻薄" }
        ];

        const links = [
            { source: "贵儿", target: "小翠", type: "爱情", strength: 85, color: "#e74c3c", label: "暗恋/真爱" },
            { source: "小翠", target: "贵儿", type: "感激", strength: 70, color: "#f39c12", label: "感激/依恋" },
            { source: "三叔公", target: "贵儿", type: "保护", strength: 80, color: "#3498db", label: "指导/保护" },
            { source: "贵儿父亲", target: "贵儿", type: "关爱", strength: 75, color: "#2ecc71", label: "父爱" },
            { source: "小翠", target: "虎子", type: "仇恨", strength: 100, color: "#8e44ad", label: "复仇杀死" },
            { source: "小翠", target: "二狗", type: "仇恨", strength: 100, color: "#8e44ad", label: "复仇杀死" },
            { source: "小翠", target: "阿彪", type: "仇恨", strength: 100, color: "#8e44ad", label: "复仇杀死" },
            { source: "连香", target: "小翠", type: "冷漠", strength: 30, color: "#95a5a6", label: "母女关系" },
            { source: "虎子", target: "二狗", type: "同伙", strength: 60, color: "#e67e22", label: "共犯" },
            { source: "二狗", target: "阿彪", type: "同伙", strength: 60, color: "#e67e22", label: "共犯" },
            { source: "虎子", target: "阿彪", type: "同伙", strength: 60, color: "#e67e22", label: "共犯" },
            { source: "三叔公", target: "贵儿父亲", type: "合作", strength: 50, color: "#16a085", label: "村中关系" }
        ];

        // SVG设置
        const svg = d3.select("#graph");
        const width = parseInt(svg.style("width"));
        const height = parseInt(svg.style("height"));

        // 添加箭头标记
        svg.append("defs").append("marker")
            .attr("id", "arrowhead")
            .attr("viewBox", "0 -5 10 10")
            .attr("refX", 15)
            .attr("refY", 0)
            .attr("markerWidth", 6)
            .attr("markerHeight", 6)
            .attr("orient", "auto")
            .append("path")
            .attr("d", "M0,-5L10,0L0,5")
            .attr("fill", "#fff");

        // 力导向图设置
        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(d => 150 - d.strength))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(d => d.size + 10));

        // 绘制连接线
        const link = svg.append("g")
            .selectAll("line")
            .data(links)
            .enter().append("line")
            .attr("class", "link")
            .attr("stroke", d => d.color)
            .attr("stroke-width", d => Math.sqrt(d.strength / 10));

        // 绘制连接线标签
        const linkLabel = svg.append("g")
            .selectAll("text")
            .data(links)
            .enter().append("text")
            .attr("class", "link-label")
            .text(d => d.label);

        // 绘制节点
        const node = svg.append("g")
            .selectAll("circle")
            .data(nodes)
            .enter().append("circle")
            .attr("class", "node")
            .attr("r", d => d.size)
            .attr("fill", d => d.color)
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        // 绘制节点标签
        const nodeLabel = svg.append("g")
            .selectAll("text")
            .data(nodes)
            .enter().append("text")
            .attr("class", "node-label")
            .text(d => d.id)
            .attr("dy", d => d.size + 15);

        // 工具提示
        const tooltip = d3.select("#tooltip");

        // 鼠标事件
        node.on("mouseover", function(event, d) {
            tooltip.style("opacity", 1)
                .html(`<strong>${d.id}</strong><br/>${d.description}<br/>类型: ${d.group}`)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        })
        .on("mouseout", function() {
            tooltip.style("opacity", 0);
        });

        link.on("mouseover", function(event, d) {
            tooltip.style("opacity", 1)
                .html(`<strong>${d.source.id} → ${d.target.id}</strong><br/>关系: ${d.type}<br/>强度: ${d.strength}/100`)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px");
        })
        .on("mouseout", function() {
            tooltip.style("opacity", 0);
        });

        // 更新位置
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            linkLabel
                .attr("x", d => (d.source.x + d.target.x) / 2)
                .attr("y", d => (d.source.y + d.target.y) / 2);

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            nodeLabel
                .attr("x", d => d.x)
                .attr("y", d => d.y);
        });

        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
    </script>
</body>
</html>