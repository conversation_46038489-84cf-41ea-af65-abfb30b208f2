<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《死亡麻辣烫》角色关系图谱</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 30px;
        }
        .graph-container {
            position: relative;
            width: 100%;
            height: 600px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }
        .character {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        .character:hover {
            transform: scale(1.1);
            z-index: 10;
        }
        .protagonist {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
        }
        .antagonist {
            background: linear-gradient(45deg, #2d3436, #636e72);
            top: 200px;
            right: 100px;
        }
        .victim {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            top: 350px;
            left: 150px;
        }
        .survivor {
            background: linear-gradient(45deg, #00b894, #00cec9);
            top: 350px;
            right: 150px;
        }
        .supernatural {
            background: linear-gradient(45deg, #6c5ce7, #a29bfe);
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 150px;
        }
        .relationship-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, transparent, #fff, transparent);
            transform-origin: left center;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .relationship-line:hover {
            opacity: 1;
            height: 5px;
        }
        .trust { background: linear-gradient(90deg, transparent, #00b894, transparent); }
        .fear { background: linear-gradient(90deg, transparent, #e17055, transparent); }
        .control { background: linear-gradient(90deg, transparent, #6c5ce7, transparent); }
        .friendship { background: linear-gradient(90deg, transparent, #74b9ff, transparent); }
        .hostility { background: linear-gradient(90deg, transparent, #d63031, transparent); }
        
        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .legend-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .legend-protagonist { border-left-color: #ff6b6b; }
        .legend-antagonist { border-left-color: #636e72; }
        .legend-victim { border-left-color: #74b9ff; }
        .legend-survivor { border-left-color: #00b894; }
        .legend-supernatural { border-left-color: #6c5ce7; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-top: 3px solid #74b9ff;
        }
        .stat-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 20px;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        .stat-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>《死亡麻辣烫》角色关系图谱</h1>
        
        <div class="graph-container" id="graphContainer">
            <!-- 角色节点 -->
            <div class="character protagonist" data-name="杨织" data-role="主角/叙述者">
                杨织<br><small>主角</small>
            </div>
            <div class="character antagonist" data-name="张田" data-role="核心配角/被控制者">
                张田<br><small>被控制者</small>
            </div>
            <div class="character victim" data-name="阿秦" data-role="牺牲者">
                阿秦<br><small>牺牲者</small>
            </div>
            <div class="character survivor" data-name="小虹" data-role="幸存者">
                小虹<br><small>幸存者</small>
            </div>
            <div class="character supernatural" data-name="怨灵集体" data-role="终极反派">
                怨灵集体<br><small>终极反派</small>
            </div>
            
            <!-- 关系连线 -->
            <div class="relationship-line trust" style="top: 120px; left: 45%; width: 200px; transform: rotate(25deg);" data-relation="杨织 ↔ 张田: 信任→恐惧→理解"></div>
            <div class="relationship-line friendship" style="top: 180px; left: 35%; width: 180px; transform: rotate(-15deg);" data-relation="杨织 ↔ 阿秦: 愧疚与友谊"></div>
            <div class="relationship-line friendship" style="top: 220px; left: 55%; width: 160px; transform: rotate(45deg);" data-relation="杨织 ↔ 小虹: 共患难友谊"></div>
            <div class="relationship-line control" style="top: 280px; left: 50%; width: 200px; transform: rotate(90deg);" data-relation="怨灵 → 张田: 控制与操纵"></div>
            <div class="relationship-line hostility" style="top: 400px; left: 40%; width: 220px; transform: rotate(-30deg);" data-relation="怨灵 ↔ 活人: 怨恨与复仇"></div>
        </div>
        
        <div class="legend">
            <div class="legend-item legend-protagonist">
                <h3>🎭 杨织 - 主角</h3>
                <p>第一人称叙述者，从好奇到恐惧再到勇敢救人的成长轨迹</p>
            </div>
            <div class="legend-item legend-antagonist">
                <h3>👻 张田 - 复杂反派</h3>
                <p>被怨灵控制的室友，最终选择自我牺牲实现救赎</p>
            </div>
            <div class="legend-item legend-victim">
                <h3>💔 阿秦 - 牺牲者</h3>
                <p>善良包容的室友，死亡成为故事重要转折点</p>
            </div>
            <div class="legend-item legend-survivor">
                <h3>🌟 小虹 - 幸存者</h3>
                <p>差点成为牺牲品，最终被杨织勇敢救出</p>
            </div>
            <div class="legend-item legend-supernatural">
                <h3>👹 怨灵集体 - 终极反派</h3>
                <p>火灾死难者的怨魂，通过麻辣烫控制活人复仇</p>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>📊 杨织 - 角色成长</h3>
                <div>好奇心: <div class="stat-bar"><div class="stat-fill" style="width: 85%; background: #ff6b6b;"></div></div></div>
                <div>勇气值: <div class="stat-bar"><div class="stat-fill" style="width: 80%; background: #00b894;"></div></div></div>
                <div>正义感: <div class="stat-bar"><div class="stat-fill" style="width: 75%; background: #74b9ff;"></div></div></div>
                <div>理智度: <div class="stat-bar"><div class="stat-fill" style="width: 70%; background: #fdcb6e;"></div></div></div>
            </div>
            
            <div class="stat-card">
                <h3>🎭 张田 - 双重性格</h3>
                <div>欺骗性: <div class="stat-bar"><div class="stat-fill" style="width: 90%; background: #636e72;"></div></div></div>
                <div>控制欲: <div class="stat-bar"><div class="stat-fill" style="width: 85%; background: #6c5ce7;"></div></div></div>
                <div>求生欲: <div class="stat-bar"><div class="stat-fill" style="width: 95%; background: #e17055;"></div></div></div>
                <div>同理心: <div class="stat-bar"><div class="stat-fill" style="width: 20%; background: #d63031;"></div></div></div>
            </div>
            
            <div class="stat-card">
                <h3>💫 关系强度矩阵</h3>
                <div>杨织 ↔ 张田: <div class="stat-bar"><div class="stat-fill" style="width: 85%; background: #e17055;"></div></div></div>
                <div>杨织 ↔ 阿秦: <div class="stat-bar"><div class="stat-fill" style="width: 75%; background: #74b9ff;"></div></div></div>
                <div>杨织 ↔ 小虹: <div class="stat-bar"><div class="stat-fill" style="width: 90%; background: #00b894;"></div></div></div>
                <div>怨灵 → 控制: <div class="stat-bar"><div class="stat-fill" style="width: 100%; background: #6c5ce7;"></div></div></div>
            </div>
            
            <div class="stat-card">
                <h3>⚡ 情节重要性</h3>
                <div>杨织(主角): <div class="stat-bar"><div class="stat-fill" style="width: 100%; background: #ff6b6b;"></div></div></div>
                <div>张田(核心): <div class="stat-bar"><div class="stat-fill" style="width: 85%; background: #636e72;"></div></div></div>
                <div>怨灵(反派): <div class="stat-bar"><div class="stat-fill" style="width: 80%; background: #6c5ce7;"></div></div></div>
                <div>阿秦(转折): <div class="stat-bar"><div class="stat-fill" style="width: 60%; background: #74b9ff;"></div></div></div>
            </div>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
    </div>
    
    <script>
        // 交互功能
        const characters = document.querySelectorAll('.character');
        const relationshipLines = document.querySelectorAll('.relationship-line');
        const tooltip = document.getElementById('tooltip');
        
        // 角色悬停效果
        characters.forEach(character => {
            character.addEventListener('mouseenter', (e) => {
                const name = e.target.dataset.name;
                const role = e.target.dataset.role;
                tooltip.innerHTML = `<strong>${name}</strong><br>${role}`;
                tooltip.style.opacity = '1';
            });
            
            character.addEventListener('mouseleave', () => {
                tooltip.style.opacity = '0';
            });
            
            character.addEventListener('mousemove', (e) => {
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 30 + 'px';
            });
        });
        
        // 关系线悬停效果
        relationshipLines.forEach(line => {
            line.addEventListener('mouseenter', (e) => {
                const relation = e.target.dataset.relation;
                tooltip.innerHTML = relation;
                tooltip.style.opacity = '1';
            });
            
            line.addEventListener('mouseleave', () => {
                tooltip.style.opacity = '0';
            });
            
            line.addEventListener('mousemove', (e) => {
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 30 + 'px';
            });
        });
        
        // 动态效果
        function animateStats() {
            const statFills = document.querySelectorAll('.stat-fill');
            statFills.forEach((fill, index) => {
                setTimeout(() => {
                    fill.style.width = fill.style.width;
                }, index * 100);
            });
        }
        
        // 页面加载完成后启动动画
        window.addEventListener('load', () => {
            setTimeout(animateStats, 500);
        });
        
        // 角色点击效果
        characters.forEach(character => {
            character.addEventListener('click', () => {
                character.style.transform += ' rotate(360deg)';
                setTimeout(() => {
                    character.style.transform = character.style.transform.replace(' rotate(360deg)', '');
                }, 600);
            });
        });
    </script>
</body>
</html>